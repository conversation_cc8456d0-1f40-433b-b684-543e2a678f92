{"author": "", "bin": {"malou-api": "./build/index.js"}, "dependencies": {"@malou-io/package-dto": "workspace:*", "@malou-io/package-utils": "workspace:*", "@modelcontextprotocol/sdk": "^1.18.2", "zod": "3"}, "description": "MCP server for Malou API integration", "devDependencies": {"@types/node": "^24.6.1", "typescript": "^5.9.3"}, "files": ["build"], "keywords": [], "license": "ISC", "main": "index.js", "name": "api-mcp", "scripts": {"build": "tsc && chmod 755 build/index.js", "dev": "tsc --watch", "start": "node build/index.js", "test": "node test-server.js"}, "type": "module", "version": "1.0.0"}