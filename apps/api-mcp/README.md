# Malou API MCP Server

A Model Context Protocol (MCP) server that provides access to <PERSON><PERSON>'s API for fetching restaurant reviews and other data.

## Features

- **get_reviews**: Fetch reviews from Malou's API with comprehensive filtering options

## Installation

1. Build the server:

```bash
npm run build
```

2. The server will be available as `malou-api` command in the build directory.

## Usage

### get_reviews Tool

Fetch reviews from Malou's API for one or more restaurants.

**Parameters:**

- `restaurant_ids` (required): Array of restaurant IDs to fetch reviews for
- `page_number` (optional): Page number (0-based, default: 0)
- `page_size` (optional): Number of reviews per page (1-100, default: 20)
- `text` (optional): Search text to filter reviews
- `ratings` (optional): Array of ratings to filter by (0-5)
- `start_date` (optional): Start date filter (ISO string)
- `end_date` (optional): End date filter (ISO string)
- `platforms` (optional): Array of platform keys to filter by
- `answered` (optional): Filter by answered reviews
- `not_answered` (optional): Filter by not answered reviews
- `pending` (optional): Filter by pending reviews
- `archived` (optional): Filter by archived reviews
- `unarchived` (optional): Filter by unarchived reviews
- `with_text` (optional): Filter reviews with text
- `without_text` (optional): Filter reviews without text
- `show_private` (optional): Include private reviews
- `answerable` (optional): Filter by answerable reviews

**Example:**

```json
{
    "restaurant_ids": ["507f1f77bcf86cd799439011"],
    "page_size": 10,
    "ratings": [4, 5],
    "with_text": true
}
```

## Configuration

The server is configured to connect to the Malou API at `http://localhost:3000/api`. You can modify the `MALOU_API_BASE` constant in the source code to point to a different API endpoint.

## API Endpoint

The server uses the `/reviews/v2` endpoint from the Malou API, which corresponds to the `handleGetRestaurantsReviewsV2` controller method.

## Development

To modify the server:

1. Edit `src/index.ts`
2. Run `npm run build` to compile
3. Test the server with your MCP client

## Response Format

The `get_reviews` tool returns formatted text containing:

- Pagination information
- Review details including:
    - Review ID
    - Restaurant ID
    - Rating
    - Platform
    - Creation date
    - Status (answered/pending/not answered)
    - Archive status
    - Review text (truncated to 200 characters)
